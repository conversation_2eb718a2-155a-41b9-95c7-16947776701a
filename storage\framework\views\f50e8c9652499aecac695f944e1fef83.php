<?php $__env->startSection('content'); ?>
    <!-- Header/Navigation -->
    <?php if (isset($component)) { $__componentOriginal2a2e454b2e62574a80c8110e5f128b60 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2a2e454b2e62574a80c8110e5f128b60 = $attributes; } ?>
<?php $component = App\View\Components\Header::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Header::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2a2e454b2e62574a80c8110e5f128b60)): ?>
<?php $attributes = $__attributesOriginal2a2e454b2e62574a80c8110e5f128b60; ?>
<?php unset($__attributesOriginal2a2e454b2e62574a80c8110e5f128b60); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2a2e454b2e62574a80c8110e5f128b60)): ?>
<?php $component = $__componentOriginal2a2e454b2e62574a80c8110e5f128b60; ?>
<?php unset($__componentOriginal2a2e454b2e62574a80c8110e5f128b60); ?>
<?php endif; ?>

    <!-- Hero Section -->
    <?php if (isset($component)) { $__componentOriginal20742eb2771d985bdc9eeee85f5ff6b5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal20742eb2771d985bdc9eeee85f5ff6b5 = $attributes; } ?>
<?php $component = App\View\Components\Hero::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Hero::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal20742eb2771d985bdc9eeee85f5ff6b5)): ?>
<?php $attributes = $__attributesOriginal20742eb2771d985bdc9eeee85f5ff6b5; ?>
<?php unset($__attributesOriginal20742eb2771d985bdc9eeee85f5ff6b5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal20742eb2771d985bdc9eeee85f5ff6b5)): ?>
<?php $component = $__componentOriginal20742eb2771d985bdc9eeee85f5ff6b5; ?>
<?php unset($__componentOriginal20742eb2771d985bdc9eeee85f5ff6b5); ?>
<?php endif; ?>

    <!-- Purpose Section -->
    <?php if (isset($component)) { $__componentOriginal9ef9c9be14772e7b76eec669e385889d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ef9c9be14772e7b76eec669e385889d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.purpose','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('purpose'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ef9c9be14772e7b76eec669e385889d)): ?>
<?php $attributes = $__attributesOriginal9ef9c9be14772e7b76eec669e385889d; ?>
<?php unset($__attributesOriginal9ef9c9be14772e7b76eec669e385889d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ef9c9be14772e7b76eec669e385889d)): ?>
<?php $component = $__componentOriginal9ef9c9be14772e7b76eec669e385889d; ?>
<?php unset($__componentOriginal9ef9c9be14772e7b76eec669e385889d); ?>
<?php endif; ?>

    <!-- Clients Section -->
    <?php if (isset($component)) { $__componentOriginal2ff411554db65cc5bbf505b2a92e34f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2ff411554db65cc5bbf505b2a92e34f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.client','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('client'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2ff411554db65cc5bbf505b2a92e34f9)): ?>
<?php $attributes = $__attributesOriginal2ff411554db65cc5bbf505b2a92e34f9; ?>
<?php unset($__attributesOriginal2ff411554db65cc5bbf505b2a92e34f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2ff411554db65cc5bbf505b2a92e34f9)): ?>
<?php $component = $__componentOriginal2ff411554db65cc5bbf505b2a92e34f9; ?>
<?php unset($__componentOriginal2ff411554db65cc5bbf505b2a92e34f9); ?>
<?php endif; ?>

    <!-- Design Solutions Section -->
    <?php if (isset($component)) { $__componentOriginal0b52c263496f5a69ee06a577099611e1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0b52c263496f5a69ee06a577099611e1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.design','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('design'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0b52c263496f5a69ee06a577099611e1)): ?>
<?php $attributes = $__attributesOriginal0b52c263496f5a69ee06a577099611e1; ?>
<?php unset($__attributesOriginal0b52c263496f5a69ee06a577099611e1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0b52c263496f5a69ee06a577099611e1)): ?>
<?php $component = $__componentOriginal0b52c263496f5a69ee06a577099611e1; ?>
<?php unset($__componentOriginal0b52c263496f5a69ee06a577099611e1); ?>
<?php endif; ?>

    <!-- Portfolio Section -->
    <?php if (isset($component)) { $__componentOriginale081b322cb04900d46907eb239a71a5b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale081b322cb04900d46907eb239a71a5b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.portfolio','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('portfolio'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale081b322cb04900d46907eb239a71a5b)): ?>
<?php $attributes = $__attributesOriginale081b322cb04900d46907eb239a71a5b; ?>
<?php unset($__attributesOriginale081b322cb04900d46907eb239a71a5b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale081b322cb04900d46907eb239a71a5b)): ?>
<?php $component = $__componentOriginale081b322cb04900d46907eb239a71a5b; ?>
<?php unset($__componentOriginale081b322cb04900d46907eb239a71a5b); ?>
<?php endif; ?>

    <!-- Pricing Section -->
    <?php if (isset($component)) { $__componentOriginalf8f47993ae8d883b42988c1d399fddfb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8f47993ae8d883b42988c1d399fddfb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.pricing','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('pricing'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8f47993ae8d883b42988c1d399fddfb)): ?>
<?php $attributes = $__attributesOriginalf8f47993ae8d883b42988c1d399fddfb; ?>
<?php unset($__attributesOriginalf8f47993ae8d883b42988c1d399fddfb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8f47993ae8d883b42988c1d399fddfb)): ?>
<?php $component = $__componentOriginalf8f47993ae8d883b42988c1d399fddfb; ?>
<?php unset($__componentOriginalf8f47993ae8d883b42988c1d399fddfb); ?>
<?php endif; ?>

    <!-- FAQ Section -->
    <?php if (isset($component)) { $__componentOriginalc457016352c9443b6633e7dc1fd2d1f7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc457016352c9443b6633e7dc1fd2d1f7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.faq','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('faq'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc457016352c9443b6633e7dc1fd2d1f7)): ?>
<?php $attributes = $__attributesOriginalc457016352c9443b6633e7dc1fd2d1f7; ?>
<?php unset($__attributesOriginalc457016352c9443b6633e7dc1fd2d1f7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc457016352c9443b6633e7dc1fd2d1f7)): ?>
<?php $component = $__componentOriginalc457016352c9443b6633e7dc1fd2d1f7; ?>
<?php unset($__componentOriginalc457016352c9443b6633e7dc1fd2d1f7); ?>
<?php endif; ?>

    <!-- CTA Section -->
    <?php if (isset($component)) { $__componentOriginala649cfbd6b1ff6fb80672d9879217508 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala649cfbd6b1ff6fb80672d9879217508 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.cta','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('cta'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala649cfbd6b1ff6fb80672d9879217508)): ?>
<?php $attributes = $__attributesOriginala649cfbd6b1ff6fb80672d9879217508; ?>
<?php unset($__attributesOriginala649cfbd6b1ff6fb80672d9879217508); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala649cfbd6b1ff6fb80672d9879217508)): ?>
<?php $component = $__componentOriginala649cfbd6b1ff6fb80672d9879217508; ?>
<?php unset($__componentOriginala649cfbd6b1ff6fb80672d9879217508); ?>
<?php endif; ?>

    <!-- Footer -->
    <?php if (isset($component)) { $__componentOriginal99051027c5120c83a2f9a5ae7c4c3cfa = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal99051027c5120c83a2f9a5ae7c4c3cfa = $attributes; } ?>
<?php $component = App\View\Components\Footer::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Footer::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal99051027c5120c83a2f9a5ae7c4c3cfa)): ?>
<?php $attributes = $__attributesOriginal99051027c5120c83a2f9a5ae7c4c3cfa; ?>
<?php unset($__attributesOriginal99051027c5120c83a2f9a5ae7c4c3cfa); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal99051027c5120c83a2f9a5ae7c4c3cfa)): ?>
<?php $component = $__componentOriginal99051027c5120c83a2f9a5ae7c4c3cfa; ?>
<?php unset($__componentOriginal99051027c5120c83a2f9a5ae7c4c3cfa); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.dark', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\portfolioissa\resources\views/welcome.blade.php ENDPATH**/ ?>
services:
  - type: web
    name: portfolioissa
    env: php
    buildCommand: |
      composer install --optimize-autoloader --no-dev
      php artisan key:generate
      touch database/database.sqlite
      php artisan migrate --force
      php artisan db:seed --force
    startCommand: php artisan serve --host 0.0.0.0 --port $PORT
    envVars:
      - key: APP_ENV
        value: production
      - key: APP_DEBUG
        value: false
      - key: APP_KEY
        generateValue: true
      - key: DB_CONNECTION
        value: sqlite
